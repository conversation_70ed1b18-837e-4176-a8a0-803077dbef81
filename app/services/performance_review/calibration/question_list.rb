# frozen_string_literal: true

module PerformanceReview
  module Calibration
    class QuestionList < ApplicationService
      prepend SimpleCommand

      def initialize(review_cycle_id)
        @review_cycle_id = review_cycle_id
        @review_cycle = ReviewCycle.find_by(id: review_cycle_id)
      end

      def call
        return errors.add(:base, 'Review Cycle not found!') if review_cycle_id.blank?

        fetch_questions
      end

      private

      attr_reader :review_cycle_id, :review_cycle

      def fetch_questions
        ReviewCycleCalibrationQuestion.reorder(Arel.sql(order_by_clause)).joins(
          'LEFT JOIN reviewer_types ON review_cycle_calibration_questions.reviewer_type_id = reviewer_types.id
          AND reviewer_types.discarded_at IS NULL
          LEFT JOIN questions ON
          review_cycle_calibration_questions.question_id = questions.id
          LEFT JOIN review_cycle_template_questions ON
          review_cycle_template_questions.question_id = questions.id
          ',
        ).where(
          review_cycle_calibration_questions: {
            review_cycle_id: review_cycle_id, visibility: %i[single multiple],
            column_type: %i[question goals_score competency_score custom_column past_review_question]
          },
          questions: {
            question_type: ['nps', 'rating_text', nil],
          },
        ).select(
            "DISTINCT review_cycle_calibration_questions.id, review_cycle_calibration_questions.question_id,
            review_cycle_calibration_questions.column_type, review_cycle_calibration_questions.column_name,
            review_cycle_calibration_questions.past_review_cycle_id,
            #{section_name_select}",
          ).group_by(&:section_name).transform_values { |cqs| cqs.map { |cq| { id: cq.id, header: cq.header, block_type: block_type(cq) } } }
      end

      def section_name_select
        "CASE
          WHEN review_cycle_calibration_questions.column_type = 'goals_score' THEN 'goals'
          WHEN review_cycle_calibration_questions.column_type = 'competency_score' THEN 'competency'
          WHEN review_cycle_calibration_questions.column_type = 'custom_column' THEN 'custom'
          WHEN review_cycle_calibration_questions.column_type = 'past_review_question' THEN 'past_review'
          WHEN review_cycle_calibration_questions.column_type = 'question' THEN reviewer_types.reviewer_type
          ELSE NULL
        END AS section_name"
      end

      def order_by_clause
        %w[custom_column question goals_score competency_score past_review_question].map do |type|
          "review_cycle_calibration_questions.column_type = '#{type}' DESC"
        end.join(', ')
      end

      def block_type(cq)
        if review_cycle.review_type == 'three_sixty_review'
          return if cq.column_type == 'past_review_question'
          return if cq.question.serviceable_type != 'Template'

          cq.question.serviceable.template_questions.find_by(question_id: cq.question_id).block_type
        else
          if cq.column_type == 'goals_score'
            'goals'
          elsif cq.column_type == 'competency_score'
            'competency'
          else
            cq.question&.review_cycle_template_questions&.first&.block_type
          end
        end
      end
    end
  end
end
